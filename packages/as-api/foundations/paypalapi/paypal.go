package paypalapi

import (
	"context"
	"strconv"
	"time"

	"as-api/as/foundations/logger"
	checkoutorders "as-api/as/foundations/paypalapi/checkout-orders"
	paymenttokens "as-api/as/foundations/paypalapi/payment-tokens"

	"github.com/pkg/errors"
)

type PayPalConfig struct {
	BaseURL      string
	ClientID     string
	ClientSecret string
}

type PayPalClient interface {
	CreateVaultSetupToken(ctx context.Context, req *CreateVaultSetupTokenRequest) (*CreateVaultSetupTokenResponse, error)
	CreateVaultPaymentToken(ctx context.Context, req *CreateVaultPaymentTokenRequest) (*CreateVaultPaymentTokenResponse, error)
}

type paypalClient struct {
	paymenttokens paymenttokens.ClientWithResponsesInterface
	orders        checkoutorders.ClientWithResponsesInterface
}

type Link struct {
	Href   string `json:"href"`
	Rel    string `json:"rel"`
	Method string `json:"method"`
}

func NewPayPalClient(config PayPalConfig, log logger.Logger) (PayPalClient, error) {
	client := ClientCredentials(config, log)

	// Create the generated client
	paymenttokens, err := paymenttokens.NewClientWithResponses(config.BaseURL, paymenttokens.WithHTTPClient(client))
	if err != nil {
		return nil, errors.Wrap(err, "failed to create PayPal client")
	}

	orders, err := checkoutorders.NewClientWithResponses(config.BaseURL, checkoutorders.WithHTTPClient(client))
	if err != nil {
		return nil, errors.Wrap(err, "failed to create PayPal client")
	}

	return &paypalClient{
		paymenttokens: paymenttokens,
		orders:        orders,
	}, nil
}

// createPalPalRequestID creates a unique request ID for PayPal API requests
func (c *paypalClient) createPalPalRequestID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 10)
}

// handleErrorResponse handles error responses from the PayPal API
func (c *paypalClient) handleErrorResponse(statusCode int, body []byte) error {
	return errors.Errorf("PayPal API request failed with status %d: %s", statusCode, string(body))
}
