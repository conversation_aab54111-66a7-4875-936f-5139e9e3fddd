package service

import (
	"as-api/as/api/user/payment/domain"
	dtos "as-api/as/dtos/user"
	"as-api/as/foundations/paypalapi"
	"as-api/as/internal/auth"
	"as-api/as/internal/payment"
	"as-api/as/internal/user"
	"as-api/as/pkg/context"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/validator"

	"github.com/pkg/errors"
)

type svc struct {
	payment      payment.PaymentDomain
	user         user.UserDomain
	auth         auth.AuthDomain
	paypalClient paypalapi.PayPalClient

	v validator.Validator
}

func NewPaymentService(payment payment.PaymentDomain, user user.UserDomain, auth auth.AuthDomain, paypalClient paypalapi.PayPalClient, v validator.Validator) domain.PaymentService {
	return &svc{
		payment:      payment,
		user:         user,
		auth:         auth,
		paypalClient: paypalClient,
		v:            v,
	}
}

func (s *svc) VerifyCreditCard(ctx context.Context, req *dtos.VerifyCreditCardRequest) (*dtos.VerifyCreditCardResponse, error) {
	if err := s.v.Validate(req); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate request: %v", err)
	}

	aid, err := context.GetAuthID(ctx)
	if err != nil {
		return nil, errors.Wrapf(apiutil.ErrPermissionDenied, "get auth id: %v", err)
	}

	auth, err := s.auth.FindOneByID(ctx, aid)
	if err != nil {
		return nil, errors.Wrap(err, "get auth")
	}

	payer := payment.Payer{
		Email: auth.Username,
	}

	if req.Address != nil {
		payer.Name = pointer.Safe(req.Address.FullName)
		if req.Address.Phone != nil {
			payer.HomePhone = pointer.Safe(convertDTOPhoneToDomainPaymentPhone(req.Address.Phone))
		}
		if req.Address.Mobile != nil {
			payer.MobilePhone = convertDTOPhoneToDomainPaymentPhone(req.Address.Mobile)
		}
	}

	if auth.UserID != nil {
		user, err := s.user.ReadOne(ctx, *auth.UserID)
		if err != nil {
			return nil, errors.Wrap(err, "get user")
		}

		if user == nil {
			return nil, errors.Wrap(apiutil.ErrResourceNotFound, "user not found")
		}

		payerDomain, err := getDomainPayerFromDomainUser(user)
		if err != nil {
			return nil, errors.Wrap(err, "get payer")
		}

		payer = pointer.Safe(payerDomain)
	}

	verifyCardReq := &payment.VerifyCardRequest{
		Token: req.Token,
		Payer: payer,
	}

	res, err := s.payment.VerifyCard(ctx, verifyCardReq)
	if err != nil {
		return nil, errors.Wrap(err, "verify card")
	}

	return &dtos.VerifyCreditCardResponse{
		AccessId:    res.AccessID,
		RedirectUrl: res.RedirectURL,
	}, nil
}

func (s *svc) CreateVaultSetupToken(ctx context.Context, req *dtos.CreateVaultSetupTokenRequest) (*dtos.CreateVaultSetupTokenResponse, error) {
	if err := s.v.Validate(req); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate request: %v", err)
	}

	// Convert DTO to PayPal API request
	paypalReq := &paypalapi.CreateVaultSetupTokenRequest{
		PaymentSource: paypalapi.PaymentSource{
			Card: req.PaymentSource.Card,
		},
	}

	if req.PaymentSource.Card == nil {
		paypalReq.PaymentSource.Card = make(map[string]interface{})
	}

	// Call PayPal API
	paypalResp, err := s.paypalClient.CreateVaultSetupToken(ctx, paypalReq)
	if err != nil {
		return nil, errors.Wrap(err, "create vault setup token")
	}

	// Convert PayPal response to DTO
	var links *[]struct {
		Href   *string `json:"href,omitempty"`
		Method *string `json:"method,omitempty"`
		Rel    *string `json:"rel,omitempty"`
	}

	if len(paypalResp.Links) > 0 {
		linkSlice := make([]struct {
			Href   *string `json:"href,omitempty"`
			Method *string `json:"method,omitempty"`
			Rel    *string `json:"rel,omitempty"`
		}, len(paypalResp.Links))

		for i, link := range paypalResp.Links {
			linkSlice[i] = struct {
				Href   *string `json:"href,omitempty"`
				Method *string `json:"method,omitempty"`
				Rel    *string `json:"rel,omitempty"`
			}{
				Href:   &link.Href,
				Rel:    &link.Rel,
				Method: &link.Method,
			}
		}
		links = &linkSlice
	}

	cardMap := make(map[string]interface{})
	paymentSource := &struct {
		Card *map[string]interface{} `json:"card,omitempty"`
	}{
		Card: &cardMap,
	}

	return &dtos.CreateVaultSetupTokenResponse{
		Id:            paypalResp.ID,
		Status:        paypalResp.Status,
		PaymentSource: paymentSource,
		Links:         links,
	}, nil
}

func (s *svc) CreateVaultPaymentToken(ctx context.Context, req *dtos.CreateVaultPaymentTokenRequest) (*dtos.CreateVaultPaymentTokenResponse, error) {
	if err := s.v.Validate(req); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate request: %v", err)
	}

	// Convert DTO to PayPal API request
	paypalReq := &paypalapi.CreateVaultPaymentTokenRequest{
		PaymentSource: paypalapi.PaymentTokenSource{
			Token: paypalapi.Token{
				ID:   req.VaultSetupToken,
				Type: "SETUP_TOKEN",
			},
		},
	}

	// Call PayPal API
	paypalResp, err := s.paypalClient.CreateVaultPaymentToken(ctx, paypalReq)
	if err != nil {
		return nil, errors.Wrap(err, "create vault payment token")
	}

	// Convert PayPal response to DTO
	var links *[]struct {
		Href   *string `json:"href,omitempty"`
		Method *string `json:"method,omitempty"`
		Rel    *string `json:"rel,omitempty"`
	}

	if len(paypalResp.Links) > 0 {
		linkSlice := make([]struct {
			Href   *string `json:"href,omitempty"`
			Method *string `json:"method,omitempty"`
			Rel    *string `json:"rel,omitempty"`
		}, len(paypalResp.Links))

		for i, link := range paypalResp.Links {
			linkSlice[i] = struct {
				Href   *string `json:"href,omitempty"`
				Method *string `json:"method,omitempty"`
				Rel    *string `json:"rel,omitempty"`
			}{
				Href:   &link.Href,
				Rel:    &link.Rel,
				Method: &link.Method,
			}
		}
		links = &linkSlice
	}

	customer := &struct {
		Id *string `json:"id,omitempty"`
	}{
		Id: &paypalResp.Customer.ID,
	}

	paymentSource := &struct {
		Card *paypalapi.CardResponse `json:"card,omitempty"`
	}{
		Card: paypalResp.PaymentSource.Card,
	}

	return &dtos.CreateVaultPaymentTokenResponse{
		Id:            paypalResp.ID,
		Status:        paypalResp.Status,
		Customer:      customer,
		PaymentSource: paymentSource,
		Links:         links,
	}, nil
}
